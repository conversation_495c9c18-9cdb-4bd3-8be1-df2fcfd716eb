import { DatePickerInput } from "@/shared/components/custom/calendar-input";
import { FormField } from "@/shared/components/ui/form";
import { Input } from "@/shared/components/ui/input";
import { Label } from "@/shared/components/ui/label";
import { Separator } from "@/shared/components/ui/separator";
import { BarcodeInput } from "@/shared/components/utils/barcode-input";
import { parsePrice } from "@/shared/utils/price-formatter";
import { Box, HelpCircle, Package2, Trash2, Calculator, AlertTriangle } from "lucide-react";
import React, { useState } from "react";
import { Controller, UseFormReturn, useWatch, type FieldError } from "react-hook-form";
import { NumericFormat } from "react-number-format";
import { NcmSelect } from "../ncm-select";
import { useCalculatedQuantity } from "../../hooks/quantity/calculate.hook";
import { ICreateStock } from "../../validators/create-stock.validator";
import { PricingCalculator } from "./pricing-calculator";
import { CategorySelect } from "@/modules/product/components/category/category-select";
import { FieldErrorWrapper } from "./enhanced-error-message";

interface InventoryItemProps {
	index: number;
	methodsForm: UseFormReturn<ICreateStock>;
	removeItem: () => void;
	isExistingIdProduct: boolean;
	isExistingIdPackage: boolean;
}

export const InventoryItem: React.FC<InventoryItemProps> = ({
	index,
	methodsForm,
	removeItem,
	isExistingIdProduct = false,
	isExistingIdPackage = false,
}) => {
	const [showPricingCalculator, setShowPricingCalculator] = useState(false);
	const readonlyClass = "bg-gray-100 cursor-not-allowed text-gray-500";

	const quantityPerPackageValue = useWatch({
		control: methodsForm.control,
		name: `inventories.${index}.stockMovement.product.package.quantityPerPackage`,
	});

	const packageQuantity = useWatch({
		control: methodsForm.control,
		name: `inventories.${index}.stockMovement.packageQuantity`,
		defaultValue: undefined,
	});

	const currentCostPrice = useWatch({
		control: methodsForm.control,
		name: `inventories.${index}.stockMovement.product.costPrice`,
	});

	const currentCategoryId = useWatch({
		control: methodsForm.control,
		name: `inventories.${index}.stockMovement.product.categoryId`,
	});

	const fieldName = `inventories.${index}.stockMovement.quantity` as const;
	const { calculatedQuantity, handleQuantityChange } = useCalculatedQuantity({
		quantityPerPackageValue,
		packageQuantity,
		setValue: methodsForm.setValue,
		fieldName,
	});

	const getError = (path: string): FieldError | undefined => {
		const error = path.split(".").reduce(
			(acc: unknown, key: string) => {
				if (acc && typeof acc === "object" && key in acc) {
					return (acc as Record<string, unknown>)[key];
				}
				return undefined;
			},
			methodsForm.formState.errors as Record<string, unknown> | undefined
		);

		return error as FieldError | undefined;
	};

	const RequiredLabel = ({ children }: { children: React.ReactNode }) => (
		<span className="flex items-center">
			{children}
			<span className="text-red-500 ml-1">*</span>
		</span>
	);

	const ConditionalRequiredLabel = ({ children }: { children: React.ReactNode }) => {
		const packageData = useWatch({
			control: methodsForm.control,
			name: `inventories.${index}.stockMovement.product.package`,
		});

		const hasAnyPackageField = Boolean(
			packageData?.name ||
				packageData?.barcode ||
				packageData?.code ||
				(packageData?.quantityPerPackage !== undefined && packageData?.quantityPerPackage !== null)
		);

		const isRequired = !isExistingIdPackage && hasAnyPackageField;

		return (
			<span className="flex items-center">
				{children}
				{isRequired && <span className="text-red-500 ml-1">*</span>}
			</span>
		);
	};

	const isAutoFilled = isExistingIdProduct || isExistingIdPackage;

	// Desabilitar CategorySelect se o produto for preenchido automaticamente E tiver categoria
	const isCategoryDisabled = isExistingIdProduct && Boolean(currentCategoryId);

	// Verificar se este item tem erros - função recursiva para verificar erros aninhados
	const hasNestedErrors = (obj: any): boolean => {
		if (!obj) return false;
		if (typeof obj === "object") {
			return Object.values(obj).some(value => {
				if (value && typeof value === "object" && "message" in value) {
					return true; // É um erro do react-hook-form
				}
				return hasNestedErrors(value); // Verificar recursivamente
			});
		}
		return false;
	};

	const itemErrors = methodsForm.formState.errors.inventories?.[index];
	const hasErrors = hasNestedErrors(itemErrors);

	const containerClasses = hasErrors
		? "relative z-10 border-2 border-red-300 rounded-xl bg-red-50 p-4 mb-3 transition-all hover:shadow-sm hover:shadow-red-200/50"
		: isAutoFilled
			? "relative z-10 border border-blue-300 rounded-xl bg-blue-50 p-4 mb-3 transition-all hover:shadow-sm hover:shadow-blue-200/50"
			: "relative z-10 border border-gray-200 rounded-xl bg-gray-50 p-4 mb-3 transition-all hover:shadow-sm";

	return (
		<div className={containerClasses}>
			<div className="flex items-center justify-between mb-3">
				<h4
					className={`text-sm font-semibold flex items-center space-x-1 ${
						hasErrors ? "text-red-700" : isAutoFilled ? "text-blue-700" : "text-gray-600"
					}`}
				>
					{hasErrors ? (
						<AlertTriangle size={16} className="text-red-600" />
					) : (
						<Box size={16} className={isAutoFilled ? "text-blue-600" : "text-gray-500"} />
					)}
					<span>Item {index + 1}</span>
					{hasErrors && <span className="ml-2 px-2 py-0.5 text-xs bg-red-200 text-red-800 rounded-full font-medium">Com erros</span>}
					{isAutoFilled && !hasErrors && (
						<span className="ml-2 px-2 py-0.5 text-xs bg-blue-200 text-blue-800 rounded-full font-medium">
							Preenchido automaticamente
						</span>
					)}
				</h4>
				<button
					type="button"
					onClick={removeItem}
					className="text-red-500 hover:text-red-700 p-1 rounded-full hover:bg-red-50"
					title="Remover este item"
				>
					<Trash2 size={16} />
				</button>
			</div>
			<p className={`text-xs font-bold flex items-center mb-2 ${isAutoFilled ? "text-blue-600" : "text-gray-500"}`}>
				<Package2 size={14} className={`mr-1 ${isAutoFilled ? "text-blue-600" : ""}`} />
				Detalhes do Produto
			</p>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-2">
				<div>
					<Label className="text-xs" htmlFor={`inventories.${index}.stockMovement.product.name`}>
						<RequiredLabel>Nome do Produto</RequiredLabel>
					</Label>
					<FieldErrorWrapper
						error={getError(`inventories.${index}.stockMovement.product.name`)?.message}
						fieldName="Nome do Produto"
						hasError={Boolean(getError(`inventories.${index}.stockMovement.product.name`))}
					>
						<Controller
							name={`inventories.${index}.stockMovement.product.name`}
							control={methodsForm.control}
							render={({ field }) => (
								<Input
									type="text"
									placeholder="Ex: Sabão em pó"
									readOnly={isExistingIdProduct}
									className={`text-sm ${isExistingIdProduct ? readonlyClass : ""}`}
									{...field}
								/>
							)}
						/>
					</FieldErrorWrapper>
				</div>

				<div>
					<Label className="text-xs flex items-center">
						<RequiredLabel>
							Código de Barras
							<div className="relative group inline-block">
								<HelpCircle className="ml-1 w-3 h-3 text-gray-400" />
								<span className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 hidden w-max rounded bg-gray-700 p-1 text-xs text-white group-hover:block">
									Código de barras do produto
								</span>
							</div>
						</RequiredLabel>
					</Label>
					<Controller
						name={`inventories.${index}.stockMovement.product.barcode`}
						control={methodsForm.control}
						render={({ field }) => (
							<BarcodeInput
								value={field.value || ""}
								onChange={field.onChange}
								readOnly={isExistingIdProduct}
								error={getError(`inventories.${index}.stockMovement.product.barcode`)?.message}
							/>
						)}
					/>
				</div>
			</div>
			<div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
				<div className="flex flex-col md:flex-row gap-2">
					<div className="w-full md:w-1/2">
						<Label className="text-xs">
							<RequiredLabel>Preço de Custo</RequiredLabel>
						</Label>{" "}
						<Controller
							name={`inventories.${index}.stockMovement.product.costPrice`}
							control={methodsForm.control}
							render={({ field }) => (
								<NumericFormat
									value={field.value ?? ""}
									onValueChange={values => field.onChange(values.floatValue ?? undefined)}
									thousandSeparator="."
									decimalSeparator=","
									decimalScale={2}
									fixedDecimalScale
									prefix="R$ "
									placeholder="R$ 0,00"
									readOnly={isExistingIdProduct}
									customInput={Input}
									className={`text-sm mt-1 w-full px-3 py-1.5 border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-400 ${
										isExistingIdProduct ? readonlyClass : ""
									} ${getError(`inventories.${index}.stockMovement.product.costPrice`) ? "border-red-500" : "border-gray-300"}`}
								/>
							)}
						/>
						{getError(`inventories.${index}.stockMovement.product.costPrice`) && (
							<span className="text-red-600 text-xs">{getError(`inventories.${index}.stockMovement.product.costPrice`)?.message}</span>
						)}
					</div>
					<div className="w-full md:w-1/2 relative">
						<Label className="text-xs">
							<RequiredLabel>Preço de Venda</RequiredLabel>
						</Label>
						<div className="relative">
							{" "}
							<Controller
								name={`inventories.${index}.stockMovement.product.price`}
								control={methodsForm.control}
								render={({ field }) => (
									<NumericFormat
										value={field.value ?? ""}
										onValueChange={values => field.onChange(values.floatValue ?? undefined)}
										thousandSeparator="."
										decimalSeparator=","
										decimalScale={2}
										fixedDecimalScale
										prefix="R$ "
										placeholder="R$ 0,00"
										readOnly={isExistingIdProduct}
										customInput={Input}
										className={`text-sm mt-1 w-full px-3 py-1.5 ${
											!isExistingIdProduct && parsePrice(currentCostPrice ?? 0) > 0 ? "pr-10" : ""
										} border rounded-md focus:outline-none focus:ring-1 focus:ring-blue-400 ${
											isExistingIdProduct ? readonlyClass : ""
										} ${getError(`inventories.${index}.stockMovement.product.price`) ? "border-red-500" : "border-gray-300"}`}
									/>
								)}
							/>
							{(() => {
								const costPriceValue = parsePrice(currentCostPrice ?? 0);
								const shouldShow = !isExistingIdProduct && costPriceValue > 0;
								return (
									shouldShow && (
										<button
											type="button"
											onClick={() => setShowPricingCalculator(!showPricingCalculator)}
											className={`absolute right-2 top-1/2 transform -translate-y-1/2 p-1 rounded-full transition-all duration-200 ${
												showPricingCalculator
													? "bg-mainColor text-white shadow-md"
													: "bg-gray-100 text-gray-500 hover:bg-mainColor/10 hover:text-mainColor"
											}`}
											title="Calculadora de preços"
										>
											<Calculator size={14} />
										</button>
									)
								);
							})()}
						</div>
						{getError(`inventories.${index}.stockMovement.product.price`) && (
							<span className="text-red-600 text-xs">{getError(`inventories.${index}.stockMovement.product.price`)?.message}</span>
						)}
						{showPricingCalculator && (
							<PricingCalculator
								index={index}
								methodsForm={methodsForm}
								isOpen={showPricingCalculator}
								onClose={() => setShowPricingCalculator(false)}
							/>
						)}
					</div>
				</div>
				<div className="flex flex-col md:flex-row gap-2">
					<div className="w-full md:w-1/2">
						<Label className="text-xs">
							<RequiredLabel>Quantidade</RequiredLabel>
						</Label>
						<FieldErrorWrapper
							error={getError(`inventories.${index}.stockMovement.quantity`)?.message}
							fieldName="Quantidade"
							hasError={Boolean(getError(`inventories.${index}.stockMovement.quantity`))}
						>
							<Controller
								name={`inventories.${index}.stockMovement.quantity`}
								control={methodsForm.control}
								render={({ field }) => (
									<Input
										type="number"
										inputMode="numeric"
										pattern="[0-9]*"
										placeholder="0"
										value={calculatedQuantity}
										onChange={e => {
											handleQuantityChange(e);
											field.onChange(e.target.value);
										}}
										ref={field.ref}
										className="text-sm no-spinner"
									/>
								)}
							/>
						</FieldErrorWrapper>
					</div>
					<div className="w-full md:w-1/2">
						<Label className="text-xs">{!isExistingIdProduct ? <RequiredLabel>Código</RequiredLabel> : "Código do fornecedor"}</Label>
						<Controller
							name={`inventories.${index}.stockMovement.product.supplierCode`}
							control={methodsForm.control}
							render={({ field }) => (
								<Input
									type="text"
									placeholder="Ex: PROD-0001"
									readOnly={isExistingIdProduct}
									className={`text-sm ${isExistingIdProduct ? readonlyClass : ""} ${
										getError(`inventories.${index}.stockMovement.product.supplierCode`) ? "border-red-500" : ""
									}`}
									{...field}
								/>
							)}
						/>
						{getError(`inventories.${index}.stockMovement.product.supplierCode`) && (
							<span className="text-red-600 text-xs">
								{getError(`inventories.${index}.stockMovement.product.supplierCode`)?.message}
							</span>
						)}
					</div>

					<div className="w-full md:w-1/2">
						<Label className="text-xs">
							<RequiredLabel>NCM</RequiredLabel>
						</Label>
						<Controller
							name={`inventories.${index}.stockMovement.product.ncm`}
							control={methodsForm.control}
							render={({ field }) =>
								isExistingIdProduct ? (
									<FieldErrorWrapper
										error={getError(`inventories.${index}.stockMovement.product.ncm`)?.message}
										fieldName="NCM"
										hasError={Boolean(getError(`inventories.${index}.stockMovement.product.ncm`))}
									>
										<Input
											type="text"
											placeholder="Ex: 1234.56.78"
											readOnly={true}
											className={`text-sm ${readonlyClass}`}
											{...field}
										/>
									</FieldErrorWrapper>
								) : (
									<NcmSelect
										value={field.value || ""}
										onChange={field.onChange}
										error={getError(`inventories.${index}.stockMovement.product.ncm`)?.message}
										placeholder="Buscar NCM..."
									/>
								)
							}
						/>
					</div>
				</div>
				<div className="flex flex-col md:flex-row gap-2 mt-2">
					<div className="w-full">
						<Controller
							name={`inventories.${index}.stockMovement.product.categoryId`}
							control={methodsForm.control}
							render={({ field }) => (
								<CategorySelect
									value={field.value?.toString() || null}
									onChange={value => {
										field.onChange(value ? parseInt(value) : undefined);
									}}
									label="Categoria"
									placeholder="Selecione uma categoria..."
									required={false}
									error={getError(`inventories.${index}.stockMovement.product.categoryId`)?.message}
									disabled={isCategoryDisabled}
								/>
							)}
						/>
					</div>
				</div>
				<div className="flex flex-col md:flex-row md:items-end gap-2 mt-2">
					<div className="w-full md:mt-2">
						<FormField
							name={`inventories.${index}.expirationDate`}
							control={methodsForm.control}
							render={({ field }) => (
								<DatePickerInput
									className="w-full"
									field={field}
									inputDateClassName={`text-sm ${getError(`inventories.${index}.expirationDate`) ? "border-red-500" : ""}`}
									label="Validade (Opcional)"
									labelClassName="text-xs"
									disabled={false}
								/>
							)}
						/>
					</div>
				</div>
			</div>
			<Separator className={`my-4 ${isAutoFilled ? "bg-blue-200" : "bg-gray-200"}`} />
			<p className={`text-xs font-bold flex items-center mb-2 ${isAutoFilled ? "text-blue-600" : "text-gray-500"}`}>
				<Package2 size={14} className={`mr-1 ${isAutoFilled ? "text-blue-600" : ""}`} />
				Detalhes da Caixa
			</p>
			<div className="grid grid-cols-1 md:grid-cols-3 gap-2">
				<div>
					<Label className="text-xs">
						<ConditionalRequiredLabel>Nome da Caixa</ConditionalRequiredLabel>
					</Label>
					<FieldErrorWrapper
						error={getError(`inventories.${index}.stockMovement.product.package.name`)?.message}
						fieldName="Nome da Caixa"
						hasError={Boolean(getError(`inventories.${index}.stockMovement.product.package.name`))}
					>
						<Controller
							name={`inventories.${index}.stockMovement.product.package.name`}
							control={methodsForm.control}
							render={({ field }) => (
								<Input
									type="text"
									placeholder="Ex: Caixa Sabão 12 un."
									readOnly={isExistingIdPackage}
									className={`text-sm ${isExistingIdPackage ? readonlyClass : ""}`}
									{...field}
								/>
							)}
						/>
					</FieldErrorWrapper>
				</div>
				<div>
					<Label className="text-xs flex items-center">
						<ConditionalRequiredLabel>
							Código de Barras da Caixa
							<div className="relative group inline-block">
								<HelpCircle className="ml-1 w-3 h-3 text-gray-400" />
								<span className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1 hidden w-max rounded bg-gray-700 p-1 text-xs text-white group-hover:block">
									Código de barras da embalagem maior (caixa).
								</span>
							</div>
						</ConditionalRequiredLabel>
					</Label>
					<FieldErrorWrapper
						error={getError(`inventories.${index}.stockMovement.product.package.barcode`)?.message}
						fieldName="Código de Barras da Caixa"
						hasError={Boolean(getError(`inventories.${index}.stockMovement.product.package.barcode`))}
					>
						<Controller
							name={`inventories.${index}.stockMovement.product.package.barcode`}
							control={methodsForm.control}
							render={({ field }) => (
								<Input
									type="string"
									placeholder="Ex: 123456789012345"
									readOnly={isExistingIdPackage}
									className={`text-sm ${isExistingIdPackage ? readonlyClass : ""}`}
									{...field}
								/>
							)}
						/>
					</FieldErrorWrapper>
				</div>
				<div>
					<Label className="text-xs">
						<ConditionalRequiredLabel>Código da Caixa</ConditionalRequiredLabel>
					</Label>
					<FieldErrorWrapper
						error={getError(`inventories.${index}.stockMovement.product.package.code`)?.message}
						fieldName="Código da Caixa"
						hasError={Boolean(getError(`inventories.${index}.stockMovement.product.package.code`))}
					>
						<Controller
							name={`inventories.${index}.stockMovement.product.package.code`}
							control={methodsForm.control}
							render={({ field }) => (
								<Input
									type="text"
									placeholder="Ex: CX-0001"
									readOnly={isExistingIdPackage}
									className={`text-sm ${isExistingIdPackage ? readonlyClass : ""}`}
									{...field}
								/>
							)}
						/>
					</FieldErrorWrapper>
				</div>
				<div>
					<Label className="text-xs">
						<ConditionalRequiredLabel>Itens por Caixa</ConditionalRequiredLabel>
					</Label>
					<FieldErrorWrapper
						error={getError(`inventories.${index}.stockMovement.product.package.quantityPerPackage`)?.message}
						fieldName="Itens por Caixa"
						hasError={Boolean(getError(`inventories.${index}.stockMovement.product.package.quantityPerPackage`))}
					>
						<Controller
							name={`inventories.${index}.stockMovement.product.package.quantityPerPackage`}
							control={methodsForm.control}
							render={({ field }) => (
								<Input
									type="number"
									inputMode="numeric"
									pattern="[0-9]*"
									placeholder="Ex: 12"
									className="text-sm no-spinner"
									{...field}
								/>
							)}
						/>
					</FieldErrorWrapper>
				</div>
			</div>
		</div>
	);
};
