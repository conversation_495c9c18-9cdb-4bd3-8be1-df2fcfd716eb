import { useEffect, useState } from "react";
import { Path, UseFormSetValue } from "react-hook-form";
import { ICreateStock } from "../../validators/create-stock.validator";

interface UseCalculatedQuantityProps {
	quantityPerPackageValue: number | string | undefined;
	packageQuantity: number | string | undefined;
	setValue: UseFormSetValue<ICreateStock>;
	fieldName: Path<ICreateStock>;
}

export const useCalculatedQuantity = ({ quantityPerPackageValue, packageQuantity, setValue, fieldName }: UseCalculatedQuantityProps) => {
	const [calculatedQuantity, setCalculatedQuantity] = useState<number | string>("");
	const [isManual, setIsManual] = useState(false);
	const MIN_PACKAGE_QUANTITY = 2;

	useEffect(() => {
		setIsManual(false);
	}, [quantityPerPackageValue]);

	useEffect(() => {
		if (!isManual) {
			if (quantityPerPackageValue === "" || packageQuantity === "" || quantityPerPackageValue === undefined || packageQuantity === undefined) {
				return;
			}

			const parsedQuantityPerPackage = Number(quantityPerPackageValue);
			const parsedPackageQuantity = Number(packageQuantity);

			if (!isNaN(parsedQuantityPerPackage) && !isNaN(parsedPackageQuantity)) {
				let newValue: number;
				if (parsedPackageQuantity >= MIN_PACKAGE_QUANTITY) {
					newValue = parsedQuantityPerPackage * parsedPackageQuantity;
				} else {
					newValue = parsedPackageQuantity;
				}
				setCalculatedQuantity(newValue);
				setValue(fieldName, newValue);
			}
		}
	}, [quantityPerPackageValue, packageQuantity, isManual, setValue, fieldName]);

	const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const value = e.target.value;
		if (value === "") {
			setIsManual(true);
			setCalculatedQuantity("");
			setValue(fieldName, undefined as any);
		} else {
			setIsManual(true);
			const numericValue = Number(value);
			setCalculatedQuantity(value);
			setValue(fieldName, numericValue);
		}
	};

	return { calculatedQuantity, handleQuantityChange };
};
